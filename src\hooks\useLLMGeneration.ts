import { useState } from 'react';
import { llmService } from '../services/llmService';
import { supabase } from '../lib/supabase';
import { Subject } from './useSubjects';
import { Topic } from './useTopics';
import { Flashcard } from './useFlashcards';
import { useAuth } from '../contexts/AuthContext';

// Interface for generated subject
export interface GeneratedSubject {
  name: string;
  code: string;
  description: string;
  color_hex: string;
  icon_name: string;
}

// Interface for generated topic
export interface GeneratedTopic {
  title: string;
  description: string;
  content: string;
  difficulty_level: number;
  estimated_study_time_minutes: number;
  learning_objectives: string[];
}

// Interface for quiz question
export interface QuizQuestion {
  question_text: string;
  options: string[];
  correct_answer_index: number;
  explanation: string;
  difficulty_level: number;
}

// Interface for generated quiz
export interface GeneratedQuiz {
  title: string;
  description: string;
  difficulty_level: number;
  time_limit_minutes: number;
  questions: QuizQuestion[];
}

/**
 * Hook for generating and saving subjects using LLM
 */
export function useSubjectGeneration() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { session } = useAuth(); // Get the auth session for the token

  /**
   * Generate a subject based on a prompt
   * @param prompt The prompt describing the subject to generate
   * @returns The generated subject or null if there was an error
   */
  const generateSubject = async (prompt: string): Promise<GeneratedSubject | null> => {
    try {
      setLoading(true);
      setError(null);
      
      // Check if user is authenticated before making the API call
      if (!session || !session.access_token) {
        throw new Error('Authentication required. Please log in to generate content.');
      }
      
      const systemPrompt = `
        Generate a detailed IGCSE subject based on the following prompt.
        Return a JSON object with these fields:
        - name: The name of the subject
        - code: A short code for the subject (e.g., "MATH" for Mathematics)
        - description: A detailed description of what the subject covers (2-3 paragraphs)
        - color_hex: A suitable hex color code for the subject (e.g., "#4285F4")
        - icon_name: A suitable icon name from common icon sets (e.g., "book", "calculator", "flask")
      `;
      
      const fullPrompt = `${systemPrompt}\n\nPrompt: ${prompt}`;
      
      console.log('Generating subject with prompt:', prompt);
      console.log('Auth session available:', !!session);
      console.log('Auth token available:', !!session?.access_token);
      
      // Include the auth token in the API call
      const result = await llmService.generateJSON<GeneratedSubject>(fullPrompt, {
        authToken: session.access_token
      });
      
      console.log('Subject generation successful:', result);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate subject';
      setError(errorMessage);
      console.error('Error generating subject:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Save a generated subject to the database
   * @param subject The subject to save
   * @returns The saved subject data or null if there was an error
   */
  const saveSubject = async (subject: GeneratedSubject): Promise<Subject | null> => {
    try {
      setLoading(true);
      setError(null);
      
      // Get the maximum display_order to place the new subject at the end
      const { data: maxOrderData } = await supabase
        .from('subjects')
        .select('display_order')
        .order('display_order', { ascending: false })
        .limit(1);
      
      const maxOrder = maxOrderData && maxOrderData.length > 0 ? maxOrderData[0].display_order : 0;
      
      // Insert the new subject
      const { data, error: dbError } = await supabase
        .from('subjects')
        .insert({
          name: subject.name,
          code: subject.code,
          description: subject.description,
          color_hex: subject.color_hex,
          icon_name: subject.icon_name,
          display_order: maxOrder + 1,
        })
        .select()
        .single();
      
      if (dbError) throw new Error(dbError.message);
      
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save subject';
      setError(errorMessage);
      console.error('Error saving subject:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { generateSubject, saveSubject, loading, error };
}

/**
 * Hook for generating and saving topics using LLM
 */
export function useTopicGeneration() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { session } = useAuth(); // Get the auth session for the token

  /**
   * Generate a topic based on a subject and topic name
   * @param subjectName The name of the subject
   * @param topicName The name of the topic to generate
   * @returns The generated topic or null if there was an error
   */
  const generateTopic = async (
    subjectName: string, 
    topicName: string
  ): Promise<GeneratedTopic | null> => {
    try {
      setLoading(true);
      setError(null);
      
      // Check if user is authenticated before making the API call
      if (!session || !session.access_token) {
        throw new Error('Authentication required. Please log in to generate content.');
      }
      
      const systemPrompt = `
        Generate a detailed IGCSE topic for the subject "${subjectName}" with the title "${topicName}".
        Return a JSON object with these fields:
        - title: The title of the topic (use "${topicName}" or improve it)
        - description: A brief description of the topic (1-2 sentences)
        - content: Detailed educational content for this topic (at least 500 words, with markdown formatting)
        - difficulty_level: A number from 1-5 representing difficulty (1=easiest, 5=hardest)
        - estimated_study_time_minutes: Estimated time to study this topic in minutes
        - learning_objectives: An array of 3-5 learning objectives for this topic
      `;
      
      console.log('Generating topic with subject and topic name:', subjectName, topicName);
      console.log('Auth session available:', !!session);
      console.log('Auth token available:', !!session?.access_token);
      
      // Include the auth token in the API call
      const result = await llmService.generateJSON<GeneratedTopic>(systemPrompt, {
        authToken: session.access_token
      });
      
      console.log('Topic generation successful:', result);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate topic';
      setError(errorMessage);
      console.error('Error generating topic:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Save a generated topic to the database
   * @param subjectId The ID of the subject this topic belongs to
   * @param topic The topic to save
   * @returns The saved topic data or null if there was an error
   */
  const saveTopic = async (
    subjectId: string, 
    topic: GeneratedTopic
  ): Promise<Topic | null> => {
    try {
      setLoading(true);
      setError(null);
      
      // First verify that the subject exists
      const { data: subjectData, error: subjectError } = await supabase
        .from('subjects')
        .select('id')
        .eq('id', subjectId)
        .single();
      
      if (subjectError || !subjectData) {
        throw new Error(`Subject with ID ${subjectId} not found. Please select a valid subject.`);
      }
      
      // Get the maximum display_order for this subject
      const { data: maxOrderData } = await supabase
        .from('topics')
        .select('display_order')
        .eq('subject_id', subjectId)
        .order('display_order', { ascending: false })
        .limit(1);
      
      const maxOrder = maxOrderData && maxOrderData.length > 0 ? maxOrderData[0].display_order : 0;
      
      // Create a slug from the title
      const slug = topic.title
        .toLowerCase()
        .replace(/[^\w\s]/g, '')
        .replace(/\s+/g, '-');
      
      // Insert the new topic
      const { data, error: dbError } = await supabase
        .from('topics')
        .insert({
          subject_id: subjectId,
          parent_topic_id: null,
          title: topic.title,
          slug,
          description: topic.description,
          content: topic.content,
          difficulty_level: topic.difficulty_level,
          estimated_study_time_minutes: topic.estimated_study_time_minutes,
          learning_objectives: topic.learning_objectives,
          display_order: maxOrder + 1,
          is_published: true,
        })
        .select()
        .single();
      
      if (dbError) throw new Error(dbError.message);
      
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save topic';
      setError(errorMessage);
      console.error('Error saving topic:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { generateTopic, saveTopic, loading, error };
}

/**
 * Hook for generating and saving flashcards using LLM
 */
export function useFlashcardGeneration() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { session } = useAuth(); // Get the auth session for the token

  /**
   * Generate flashcards for a topic
   * @param topicTitle The title of the topic
   * @param topicContent The content of the topic to use as context
   * @param count The number of flashcards to generate
   * @returns Array of generated flashcards or null if there was an error
   */
  const generateFlashcards = async (
    topicTitle: string,
    topicContent: string,
    count: number = 5
  ): Promise<Partial<Flashcard>[] | null> => {
    try {
      setLoading(true);
      setError(null);

      // Check if user is authenticated before making the API call
      if (!session || !session.access_token) {
        throw new Error('Authentication required. Please log in to generate content.');
      }

      const systemPrompt = `
        Generate ${count} flashcards for the IGCSE topic "${topicTitle}" based on the following content.
        Use this content as reference: "${topicContent.substring(0, 1000)}..."

        Return a JSON array of flashcard objects, each with these fields:
        - front_content: The question or prompt on the front of the card
        - back_content: The answer on the back of the card
        - card_type: One of "basic", "cloze", or "multiple_choice"
        - difficulty_level: A number from 1-5 representing difficulty
        - tags: An array of relevant tags for this flashcard
        - hint: A hint to help remember the answer (optional)
        - explanation: A detailed explanation of the answer (optional)
      `;

      console.log('Generating flashcards with topic title and content:', topicTitle, topicContent);
      console.log('Auth session available:', !!session);
      console.log('Auth token available:', !!session?.access_token);
      console.log('User email:', session?.user?.email);
      console.log('User role:', session?.user?.app_metadata?.role);

      // Include the auth token in the API call
      const result = await llmService.generateJSON<any>(systemPrompt, {
        maxTokens: 2000,
        authToken: session.access_token
      });

      console.log('Flashcard generation successful:', result);

      // Handle different response formats from the API
      if (Array.isArray(result)) {
        // Direct array format
        return result as Partial<Flashcard>[];
      } else if (result && typeof result === 'object' && 'flashcards' in result) {
        // Wrapped format: {flashcards: [...]}
        const flashcards = (result as any).flashcards;
        if (Array.isArray(flashcards)) {
          return flashcards as Partial<Flashcard>[];
        }
      }

      console.error('Unexpected API response format:', result);
      throw new Error('Received unexpected response format from the API');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate flashcards';
      setError(errorMessage);
      console.error('Error generating flashcards:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Save generated flashcards to the database
   * @param topicId The ID of the topic these flashcards belong to
   * @param flashcards The flashcards to save
   * @returns The saved flashcard data or null if there was an error
   */
  const saveFlashcards = async (
    topicId: string,
    flashcards: Partial<Flashcard>[]
  ): Promise<Flashcard[] | null> => {
    try {
      setLoading(true);
      setError(null);

      // Check authentication first
      if (!session || !session.access_token) {
        throw new Error('Authentication required. Please log in to save flashcards.');
      }

      // First verify that the topic exists
      console.log('Verifying topic existence for ID:', topicId);
      const { data: topicData, error: topicError } = await supabase
        .from('topics')
        .select('id')
        .eq('id', topicId)
        .single();
      
      if (topicError) {
        console.error('Error verifying topic:', topicError);
        throw new Error(`Failed to verify topic: ${topicError.message}`);
      }
      
      if (!topicData) {
        console.error('Topic not found:', topicId);
        throw new Error(`Topic with ID ${topicId} does not exist. Cannot save flashcards.`);
      }
      
      console.log('Topic verified successfully:', topicData);
      
      // Validate required fields in flashcards
      const invalidFlashcards = flashcards.filter(card => 
        !card.front_content || !card.back_content
      );
      
      if (invalidFlashcards.length > 0) {
        console.error('Invalid flashcards found:', invalidFlashcards);
        throw new Error('All flashcards must have front_content and back_content');
      }
      
      // Prepare flashcards for insertion
      const flashcardsToInsert = flashcards.map(card => ({
        topic_id: topicId,
        front_content: card.front_content!,
        back_content: card.back_content!,
        card_type: card.card_type || 'basic',
        difficulty_level: card.difficulty_level || 1,
        tags: card.tags || [],
        hint: card.hint,
        explanation: card.explanation,
        is_active: true,
      }));
      
      console.log('Inserting flashcards:', flashcardsToInsert);
      
      // Insert the flashcards
      const { data, error: dbError } = await supabase
        .from('flashcards')
        .insert(flashcardsToInsert)
        .select();
      
      if (dbError) {
        console.error('Database error saving flashcards:', dbError);
        
        // Check for foreign key violation
        if (dbError.code === '23503') {
          throw new Error(`Foreign key violation: The topic ID ${topicId} does not exist in the database.`);
        }
        
        throw new Error(dbError.message);
      }
      
      console.log('Flashcards saved successfully:', data);
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save flashcards';
      setError(errorMessage);
      console.error('Error saving flashcards:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { generateFlashcards, saveFlashcards, loading, error };
}

/**
 * Hook for generating and saving quizzes using LLM
 */
export function useQuizGeneration() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { session } = useAuth(); // Get the auth session for the token

  /**
   * Generate a quiz for a topic
   * @param topicTitle The title of the topic
   * @param topicContent The content of the topic to use as context
   * @param questionCount The number of questions to generate
   * @returns The generated quiz or null if there was an error
   */
  const generateQuiz = async (
    topicTitle: string,
    topicContent: string,
    questionCount: number = 10
  ): Promise<GeneratedQuiz | null> => {
    try {
      setLoading(true);
      setError(null);
      
      // Check if user is authenticated before making the API call
      if (!session || !session.access_token) {
        throw new Error('Authentication required. Please log in to generate content.');
      }
      
      const systemPrompt = `
        Generate a quiz for the IGCSE topic "${topicTitle}" with ${questionCount} questions.
        Use this content as reference: "${topicContent.substring(0, 1000)}..."
        
        Return a JSON object with these fields:
        - title: A title for the quiz
        - description: A brief description of what the quiz covers
        - difficulty_level: A number from 1-5 representing difficulty
        - time_limit_minutes: Suggested time limit in minutes
        - questions: An array of ${questionCount} question objects, each with:
          - question_text: The question text
          - options: An array of 4 possible answers
          - correct_answer_index: The index (0-3) of the correct answer
          - explanation: An explanation of why the answer is correct
          - difficulty_level: A number from 1-5 representing difficulty
      `;
      
      console.log('Generating quiz with topic title and content:', topicTitle, topicContent);
      console.log('Auth session available:', !!session);
      console.log('Auth token available:', !!session?.access_token);
      
      // Include the auth token in the API call
      const result = await llmService.generateJSON<GeneratedQuiz>(systemPrompt, { 
        maxTokens: 3000,
        authToken: session.access_token 
      });
      
      console.log('Quiz generation successful:', result);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate quiz';
      setError(errorMessage);
      console.error('Error generating quiz:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Save a generated quiz to the database
   * @param topicId The ID of the topic this quiz belongs to
   * @param quiz The quiz to save
   * @returns The saved quiz data or null if there was an error
   */
  const saveQuiz = async (
    topicId: string,
    quiz: GeneratedQuiz
  ): Promise<any | null> => {
    try {
      setLoading(true);
      setError(null);
      
      // First, create the quiz
      const { data: quizData, error: quizError } = await supabase
        .from('quizzes')
        .insert({
          topic_id: topicId,
          title: quiz.title,
          description: quiz.description,
          difficulty_level: quiz.difficulty_level,
          time_limit_minutes: quiz.time_limit_minutes,
          is_published: true,
        })
        .select()
        .single();
      
      if (quizError) throw new Error(quizError.message);
      
      // Then, create the questions
      const questionsToInsert = quiz.questions.map((question, index) => ({
        quiz_id: quizData.id,
        question_text: question.question_text,
        options: question.options,
        correct_answer: question.options[question.correct_answer_index], // Fix: use correct_answer instead of correct_answer_index
        explanation: question.explanation,
        difficulty_level: question.difficulty_level,
        display_order: index,
      }));
      
      const { data: questionsData, error: questionsError } = await supabase
        .from('quiz_questions')
        .insert(questionsToInsert)
        .select();
      
      if (questionsError) throw new Error(questionsError.message);
      
      return { quiz: quizData, questions: questionsData };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save quiz';
      setError(errorMessage);
      console.error('Error saving quiz:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { generateQuiz, saveQuiz, loading, error };
}
